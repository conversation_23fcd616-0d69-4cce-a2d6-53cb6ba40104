#!/bin/bash
ACMD="$1"
CSFTP_BIN='/usr/local/akcs/csftp/bin/vsftpd'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csftp()
{
    nohup $CSFTP_BIN >/dev/null 2>&1 &
    echo "Start vsftpd successful"
    if [ -z "`ps -fe|grep "csftprun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csftp/scripts/csftprun.sh >/dev/null 2>&1 &
    fi
}
stop_csftp()
{
    echo "Begin to stop csftprun.sh"
    csftprunid=`ps aux | grep -w csftprun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csftprunid}" ];then
	    echo "csftprun.sh is running at ${csftprunid}, will kill it first."
	    kill -9 ${csftprunid}
    fi
    echo "Begin to stop vsftpd"
	csftpid=`ps aux | grep -w vsftpd | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csftpid}" ];then
	    kill -9 ${csftpid}
    fi
    sleep 2
    echo "Stop vsftpd successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep vsftpd | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_csftp
    else
        echo "vsftpd is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep vsftpd | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "vsftpd is already stopping"
    else
        stop_csftp
    fi
    ;;
  restart)
    stop_csftp
    sleep 1
    start_csftp
    ;;
  status)
    cnt=`ss -alnp | grep vsftpd | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m vsftpd is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m vsftpd is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit



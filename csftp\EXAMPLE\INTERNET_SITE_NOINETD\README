This example shows how to run vsftpd in "standalone" mode - i.e. without
needing to run an inetd of some kind (inetd, xinetd, tcpserver etc).

vsftpd has supported standalone mode since v1.1.0.

With the release of v1.1.3, the feature list of standalone mode has grown so
that large internet sites no longer need to use an inetd.
Previously, large internet sites were forced to use xinetd for the important
feature of "limit number of concurrent connections from a single IP address".
Unfortunately, there seem to be xinetd stability issues - various larger
sites are reporting that xinetd's session counting can go wrong and incorrectly
kick off users because it thinks the FTP site is full when it is not.

vsftpd now natively handles maximum session counts and maximum session per IP
counts. It can also do native access control via tcp_wrappers integration and
even per-connect-IP configurability.

To use this example config:

1) Copy the vsftpd.conf file in this directory to /etc/vsftpd.conf.

2) Start up vsftpd, e.g.
vsftpd &

3) That should be it!

The example vsftpd.conf is based on the vsftpd.conf from the INTERNET_SITE
example. Let's look at the differences (at the top):

# Standalone mode
listen=YES

This tells vsftpd to run in standalone mode. Do NOT try and run vsftpd from
an inetd with this option set - it won't work, you may well get 500 OOPS:
could not bind listening socket.

max_clients=200
max_per_ip=4

The maximum number of session is 200 (new clients will get refused with a
busy message). The maximum number of sessions from a single IP is 4 (the
5th connect will get refused with a suitable message).


One further note on standalone mode, regarding virtual IPs. This is very
easy - just run one copy of vsftpd per virtual IP (remembering to give each
a separate config file on the command line).
Distinguish which vsftpd is for which virtual IP with a setting like this
in the vsftpd.conf:

listen_address=***********

And launch vsftpd with a specific config file like this:
vsftpd /etc/vsftpd.conf.site1 &


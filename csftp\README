This is vsftpd, version 3.0.2
Author: <PERSON>: <EMAIL>
Website: http://vsftpd.beasts.org/
- All options are documented in the vsftpd.conf.5 manual page.
- See the FAQ file for solutions to frequently asked questions.
- Visit http://vsftpd.beasts.org/ for vsftpd news and releases.

What is this?
=============

vsftpd is an FTP server, or daemon. The "vs" stands for Very Secure. Obviously
this is not a guarantee, but a reflection that I have written the entire
codebase with security in mind, and carefully designed the program to be
resilient to attack.

Recent evidence shows that vsftpd is also extremely fast and scalable. vsftpd
has achieved ~4000 concurrent users on a single machine, in a production
environment.

vsftpd is now a proven stable solution. Of particular note, RedHat used vsftpd
to enable ftp.redhat.com to support 15,000 concurrent users across their
server pool. This extreme load was generated by the release of RedHat 7.2 to
the world.
vsftpd now powers some of the largest and most prestigious sites on the
internet.

Installation
============

Please see the INSTALL file.

Configuration
=============

All configuration options are documented in the manual page vsftpd.conf.5.
Various example configurations are discussed in the EXAMPLE directory.
Frequently asked questions are tackled in the FAQ file.


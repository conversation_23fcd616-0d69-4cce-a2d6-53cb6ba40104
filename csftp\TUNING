So, you want vsftpd to go quickly?

Here are some random assorted performance tips.

1) vsftpd thrives because of its lightweight RSS and vm usage. If you run
a glibc based system (e.g. RedHat 5+), look in /etc/nsswitch.conf, and
if possible, disable the "nis" and "nisplus" options for "passwd", "shadow"
and "group". This prevents unneeded runtime libraries being added into
the vsftpd virtual memory space.

2) vsftpd will attempt to save CPU power by using sendfile() on capable
operating systems. Currently, Linux 2.2+ and FreeBSD 3.0+ use sendfile().
Consider running on these excellent operating systems.

3) Irritated by vsftpd using _two_ processes per connection? Don't be, it's
a very secure architecture. However, if you run Linux 2.4+, or Linux 2.2.19+, a
"one process" security model is possible thanks to nifty security features.
See the vsftpd.conf man page.

4) Avoid large directories (e.g. thousands of entries) if possible. Many
filesystems do not handle such cases efficiently at all. Preparing large
directory listings will require vsftpd to use moderate amounts of memory
and CPU. If you _must_ have large directories, consider either making them
unreadable, or use a filesystem which copes well with large directories such
as reiserfs.


The following FTP related references are useful:
(Implemented or partially implemented in vsftpd):
=================================================

RFC-959, original FTP spec.
http://www.rfc-editor.org/rfc/rfc959.txt

RFC-1123, the small FTP related section extends / clarifies RFC-959.
http://www.rfc-editor.org/rfc/rfc1123.txt

RFC-2228, FTP security extensions. vsftpd implements the small subset needed
to support TLS / SSL connections.
http://www.rfc-editor.org/rfc/rfc2228.txt

RFC-2389. Proposes FEAT and OPTS commands.
http://www.rfc-editor.org/rfc/rfc2389.txt

RFC-2428. Essentially IPv6 support.
http://www.rfc-editor.org/rfc/rfc2428.txt

"Securing FTP with TLS" (draft-murray-auth-ftp-ssl-09.txt). Document that
dives into the standardized behaviour of SSL / TLS connections in conjunction
with RFC-2228.
http://www.isaserver.org/articles/Securing_FTP_with_TLS.html

"Extensions to FTP" (draft-ietf-ftpext-mlst-16.txt). Standardizes SIZE, MDTM,
MLST and MLSD. Note that vsftpd has not implemented MLST and MLSD due to lack
of demand from users. Perhaps the client support just isn't there.
http://www.ietf.org/internet-drafts/draft-ietf-ftpext-mlst-16.txt

(Not implemented in vsftpd):
============================

RFC-1579. Proposes an APSV command. No users have requested this in vsftpd;
perhaps the client support just isn't there.
http://www.rfc-editor.org/rfc/rfc1579.txt

RFC-1639. Proposes commands LPRT and LPSV. Seems to be deprecated in favour of
EPRT and EPSV in RFC-2428.
http://www.rfc-editor.org/rfc/rfc1639.txt

RFC-2640. Deals with internationalization and the LANG command. I'm not seeing
any vsftpd users with requirements in this area.
http://www.rfc-editor.org/rfc/rfc2640.txt


#!/bin/bash

AKCS_INSTALL_PATH=/usr/local/akcs/csftp
AKCS_RUN_SCRIPT_NAME=csftprun.sh
AKCS_RUN_SCRIPT=${AKCS_INSTALL_PATH}/scripts/${AKCS_RUN_SCRIPT_NAME}
INSTALL_CONF=/etc/csftp_install.conf
HOST_IP=/etc/ip
WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
PAKCAGES_ROOT=${WORK_DIR}/..
chmod 777 -R ${PAKCAGES_ROOT}/*

if [ -f /usr/sbin/vsftpd ]; then
	rm -rf /usr/sbin/vsftpd
	service vsftpd stop
fi

if [ ! -d /usr/local/akcs ];then
    mkdir /usr/local/akcs
fi

#read 删除不能用问题
stty erase ^H
font_off(){
	echo -en "\033[0m"
}
blue(){
    echo -e "\033[34m$1\033[0m"
	font_off
}
green(){
    echo -e  "\033[32m$1\033[0m"
	font_off
}
red(){
    echo -e "\033[31m$1\033[0m"
	font_off
}
yellow(){
    echo -e "\033[33m$1\033[0m"
	font_off
}

install_vsftpd()
{
	cnt=`dpkg -l| grep db4.8 | wc -l`
    if [ "$cnt" -eq "0" ]; then
		sudo dpkg -i ${PAKCAGES_ROOT}/system/libdb4.8_4.8.30-trusty1_amd64.deb
		sudo dpkg -i ${PAKCAGES_ROOT}/system/libdb4.8++_4.8.30-trusty1_amd64.deb
		sudo dpkg -i ${PAKCAGES_ROOT}/system/db4.8-util_4.8.30-11ubuntu1_amd64.deb
	fi

	if [ -d /etc/vsftpd.d ]; then
		sudo rm -f /etc/vsftpd.d/vsftpd_login.db
	else
		sudo mkdir /etc/vsftpd.d
	fi

    sudo db4.8_load -T -t hash -f ${PAKCAGES_ROOT}/system/ftpvusers.txt /etc/vsftpd.d/vsftpd_login.db
    chmod 600 /etc/vsftpd.d/vsftpd_login.db

    mkdir -p /lib/security/
    ln -s /lib/x86_64-linux-gnu/security/pam_userdb.so /lib/security/pam_userdb.so

    mkdir -p /var/run/vsftpd/empty

    if [ -f /etc/pam.d/vsftpd_login ]; then
        rm -rf /etc/pam.d/vsftpd_login
    fi
    cp ${PAKCAGES_ROOT}/system/vsftpd_login /etc/pam.d/vsftpd_login

    sudo mkdir -p /home/<USER>/akuvox
    sudo useradd vsftpd -d /home/<USER>/bin/false
    sudo chown -R vsftpd:vsftpd /home/<USER>
    sudo chgrp -R vsftpd /home/<USER>
    sudo chmod -R 700 /home/<USER>

    if [ -f /etc/vsftpd.conf ]; then
        rm -rf /etc/vsftpd.conf
    fi
    cp ${PAKCAGES_ROOT}/system/vsftpd.conf /etc/vsftpd.conf

    if [ -f /etc/vsftpd.d/akuvox ]; then
        rm -rf /etc/vsftpd.d/akuvox
    fi
    cp ${PAKCAGES_ROOT}/system/akuvox /etc/vsftpd.d/akuvox
}

CheckIPAddr()
{
    echo $1|grep "^[0-9]\{1,3\}\.\([0-9]\{1,3\}\.\)\{2\}[0-9]\{1,3\}$" > /dev/null;
    #IP地址必须为全数字
    if [ $? -ne 0 ]
    then
        return 1
    fi
    ipaddr=$1
    a=`echo $ipaddr|awk -F . '{print $1}'`  #以"."分隔，取出每个列的值
    b=`echo $ipaddr|awk -F . '{print $2}'`
    c=`echo $ipaddr|awk -F . '{print $3}'`
    d=`echo $ipaddr|awk -F . '{print $4}'`
    for num in $a $b $c $d
    do
        if [ $num -gt 255 ] || [ $num -lt 0 ]    #每个数值必须在0-255之间
        then
            return 1
        fi
    done
    return 0
}

EchoHostIPAddr()
{
    inner_ip_str="SERVER_INNER_IP="
    inner_ip_cat=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    inner_ip=$inner_ip_str$inner_ip_cat
    echo $inner_ip

    outer_ipv4_str="SERVERIP="
    outer_ipv4_cat=`cat $HOST_IP | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
    outer_ipv4=$outer_ipv4_str$outer_ipv4_cat
    echo $outer_ipv4

    outer_domain_str="SERVER_DOMAIN="
    outer_doamin_cat=`cat $HOST_IP | grep -w SERVER_DOMAIN | awk -F'=' '{ print $2 }'`
    outer_doamin=$outer_domain_str$outer_doamin_cat
    echo $outer_doamin	
        
    outer_ipv6_str="SERVERIPV6="
    outer_ipv6_cat=`cat $HOST_IP | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`
    outer_ipv6=$outer_ipv6_str$outer_ipv6_cat
    echo $outer_ipv6
}

EnterHostIPAddr()
{
   #输入内网IP
    yellow "Enter your host server inner IPV4: \c"
    #不能写成这样:read $SERVER_INNER_IP;
    read SERVER_INNER_IP;

    #输入外网IP
    yellow "Enter your host server outer IPV4: \c"
    read SERVERIP;

    #输入外网域名
    yellow "Enter your host server outer Domain: \c"
    read SERVER_DOMAIN;	

    #输入IP6
    yellow "Enter your host server IPV6: \c"
    read SERVERIPV6;

    for ip in $SERVER_INNER_IP $SERVERIP ; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入主机的IP文件
    echo "" >$HOST_IP
    echo "SERVER_INNER_IP=$SERVER_INNER_IP" >>$HOST_IP
    echo "SERVERIP=$SERVERIP" >>$HOST_IP
    echo "SERVER_DOMAIN=$SERVER_DOMAIN" >>$HOST_IP
    echo "SERVERIPV6=$SERVERIPV6" >>$HOST_IP
}

EnterBasicSrvIPAddr()
{
    #输入redis内网IP
    yellow "Enter your redis server inner IPV4: \c"
    read REDIS_INNER_IP;

    #输入mysql内网IP
    yellow "Enter your mysql server inner IPV4: \c"
    read MYSQL_INNER_IP;

    #输入etcd内网IP
    yellow "Enter your etcd cluster servers inner IPV4,(eg:************:5204;************:15204;...): \c"
    read ETCD_INNER_IP;

	#延时队列内网地址IPv4
    yellow "Enter your beanstalkd server inner IPV4: \c"
    read BEANSTALKD_IP;

    for ip in $REDIS_INNER_IP $MYSQL_INNER_IP $BEANSTALKD_IP; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入基础服务的IP文件
    echo "" >$INSTALL_CONF
    echo "REDIS_INNER_IP=$REDIS_INNER_IP" >>$INSTALL_CONF
    echo "MYSQL_INNER_IP=$MYSQL_INNER_IP" >>$INSTALL_CONF
	echo "ETCD_INNER_IP=$ETCD_INNER_IP" >>$INSTALL_CONF
	echo "BEANSTALKD_IP=$BEANSTALKD_IP" >>$INSTALL_CONF
}

function Md5sumCheck()
{
	newfile=$1
	oldfile=$2
	newmd5=`md5sum $newfile|awk '{print $1}'`
	oldmd5=`md5sum $oldfile|awk '{print $1}'`
	if [ $oldmd5 != $newmd5 ];then
	echo "md5sum check error!"
	echo "$oldfile install failed!"
	exit 0

	fi
}

#added by chenyc,2019-03-29,分布式脚本，先确定本机的内外网地址,所有的ip信息放在:/etc/ip里面
if [ -f $HOST_IP ];then
    EchoHostIPAddr
    SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    SERVERIP=`cat $HOST_IP | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
    SERVER_DOMAIN=`cat $HOST_IP | grep -w SERVER_DOMAIN | awk -F'=' '{ print $2 }'`
    SERVERIPV6=`cat $HOST_IP | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`

    yellow "please comfirm the host ip information is ok(host ip must contain inner ip and outer ipv4, outer ipv6 is an option.)? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterHostIPAddr
    fi
else
    blue "Can not found host ip file </etc/ip>, please enter all information below:"
    EnterHostIPAddr
fi

EchoBasicSrvIPAddr()
{
    mysql_inner_ip_str="MYSQL_INNER_IP="
    mysql_inner_ip_cat=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    mysql_inner_ip=$mysql_inner_ip_str$mysql_inner_ip_cat
    echo $mysql_inner_ip

    redis_inner_ip_str="REDIS_INNER_IP="
    redis_inner_ip_cat=`cat $INSTALL_CONF | grep -w REDIS_INNER_IP | awk -F'=' '{ print $2 }'`
    redis_inner_ip=$redis_inner_ip_str$redis_inner_ip_cat
    echo $redis_inner_ip

	etcd_inner_ip_str="ETCD_INNER_IP="
    etcd_inner_ip_cat=`cat $INSTALL_CONF | grep -w ETCD_INNER_IP | awk -F'=' '{ print $2 }'`
    etcd_inner_ip=$etcd_inner_ip_str$etcd_inner_ip_cat
    echo $etcd_inner_ip

	beanstalkd_str="BEANSTALKD_IP="
    beanstalkd_cat=`cat $INSTALL_CONF | grep -w BEANSTALKD_IP | awk -F'=' '{ print $2 }'`
    beanstalkd_ipv4=$beanstalkd_str$beanstalkd_cat
    echo $beanstalkd_ipv4
}
#再确定redis、mysql、etcd、nsqlookupd等组件的内网ip信息
if [ -f $INSTALL_CONF ];then
    EchoBasicSrvIPAddr
    MYSQL_INNER_IP=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    REDIS_INNER_IP=`cat $INSTALL_CONF | grep -w REDIS_INNER_IP | awk -F'=' '{ print $2 }'`
	ETCD_INNER_IP=`cat $INSTALL_CONF | grep -w ETCD_INNER_IP | awk -F'=' '{ print $2 }'`
	BEANSTALKD_IP=`cat $INSTALL_CONF | grep -w BEANSTALKD_IP | awk -F'=' '{ print $2 }'`
    yellow "please comfirm the basic server inner ip information is ok? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterBasicSrvIPAddr
    fi
else
    blue "Can not found system config </etc/csftp_install.conf>, please enter all information below:"
    EnterBasicSrvIPAddr
fi

#replace serverip,注意ip后面的等号不能有空格
sed -i "s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g" ${PAKCAGES_ROOT}/csftp/conf/csftp.conf
sed -i "s/^.*outerip=.*/outerip=${SERVERIP}/g" ${PAKCAGES_ROOT}/csftp/conf/csftp.conf
sed -i "s/^.*outer_domain=.*/outer_domain=${SERVER_DOMAIN}/g" ${PAKCAGES_ROOT}/csftp/conf/csftp.conf
sed -i "s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g" ${PAKCAGES_ROOT}/csftp/conf/csftp.conf
sed -i "s/^.*dev_outerip_host=.*/dev_outerip_host=${REDIS_INNER_IP}/g" ${PAKCAGES_ROOT}/csftp/conf/csftp_redis.conf
sed -i "s/^.*beanstalk_ip=.*/beanstalk_ip=${BEANSTALKD_IP}/g" ${PAKCAGES_ROOT}/csftp/conf/csftp.conf
#pasv_address 专有网络下 填写外网地址
sed -i "s/^.*pasv_address=.*/pasv_address=${SERVERIP}/g" ${PAKCAGES_ROOT}/system/vsftpd.conf

#后于之前替换的代码,保证db redis配置不被覆盖
bash $WORK_DIR/redis-install.sh $INSTALL_CONF ${PAKCAGES_ROOT}/csftp/conf/csftp_redis.conf
bash $WORK_DIR/dbproxy-install.sh $INSTALL_CONF ${PAKCAGES_ROOT}/csftp/conf/csftp.conf

scriptpid=`ps -fe|grep "${AKCS_RUN_SCRIPT_NAME}" |grep -v grep |awk '{print $2}'`
if [ -n "${scriptpid}" ];then
	echo "${AKCS_RUN_SCRIPT_NAME} is running at ${scriptpid}, will kill it first."
	kill -9 ${scriptpid}
	sleep 2
fi

echo "stopping csftp services..."
${PAKCAGES_ROOT}/csftp_scripts/csftpctl.sh stop
sleep 1

install_vsftpd

echo "making logs directories..."
if [ ! -d /var/log/csftplog ]; then
    mkdir -p /var/log/csftplog
fi

echo "copying akcs csftp files..."
if [ -d /usr/local/akcs/csftp ]; then
    rm -rf  /usr/local/akcs/csftp
fi

if [ -d /usr/local/akcs/csftp_scripts ]; then
    rm -rf /usr/local/akcs/csftp_scripts
fi

if [ ! -d /var/run/vsftpd/empty ];then
	mkdir -p /var/run/vsftpd/empty
fi

if [ ! -d /usr/local/akcs/csstorage/data ];then
	mkdir -p /usr/local/akcs/csstorage/data
fi

chmod 777 -R /usr/local/akcs/
chmod 777 -R /usr/local/akcs/csstorage/data

cp -rf ${PAKCAGES_ROOT}/csftp/ /usr/local/akcs

#4.6新增md5sum校验，避免拷贝不完全
Md5sumCheck ${PAKCAGES_ROOT}/csftp/bin/vsftpd /usr/local/akcs/csftp/bin/vsftpd

mkdir -p /usr/local/akcs/csftp/scripts
chmod -R 777 /usr/local/akcs/csftp/scripts/
cp -rf ${PAKCAGES_ROOT}/csftp_scripts/* /usr/local/akcs/csftp/scripts/


#add run script to rc.local
if [ -z "`grep "${AKCS_RUN_SCRIPT}" /etc/init.d/rc.local`" ];then
	echo "bash ${AKCS_RUN_SCRIPT} &" >> /etc/init.d/rc.local
fi

chmod 777 ${AKCS_RUN_SCRIPT}
if [ -z "`ps -fe|grep "${AKCS_RUN_SCRIPT_NAME}" |grep -v grep`" ];then
	nohup bash ${AKCS_RUN_SCRIPT} >/dev/null 2>&1 &
	sleep 1
fi
echo "csftp install completed ..."

#core文件生效
if [ ! -d /var/core ]; then
    mkdir -p /var/core
    chmod -R 777 /var/core
fi
if [ -z "`grep "kernel.core_pattern" /etc/sysctl.conf`" ];then
	echo 'kernel.core_pattern = /var/core/core_%e_%p_%t' >> /etc/sysctl.conf
fi

if [ -z "`grep "ulimit -c unlimited" /etc/profile`" ];then
	echo 'ulimit -c unlimited' >> /etc/profile
fi

sysctl -p >/dev/null
. /etc/profile

#echo status
/usr/local/akcs/csftp/scripts/csftpctl.sh status


#!/bin/bash

install_vsftpd()
{
    if ! dpkg -l | grep db4.8; then
        dpkg -i /usr/local/akcs/csftp/system/libdb4.8_4.8.30-trusty1_amd64.deb
        dpkg -i /usr/local/akcs/csftp/system/libdb4.8++_4.8.30-trusty1_amd64.deb
        dpkg -i /usr/local/akcs/csftp/system/db4.8-util_4.8.30-11ubuntu1_amd64.deb
    fi

    if [ -d /etc/vsftpd.d ]; then
        rm -f /etc/vsftpd.d/vsftpd_login.db
    else
        mkdir /etc/vsftpd.d
    fi

    db4.8_load -T -t hash -f /usr/local/akcs/csftp/system/ftpvusers.txt /etc/vsftpd.d/vsftpd_login.db
    chmod 600 /etc/vsftpd.d/vsftpd_login.db

    mkdir -p /lib/security/
    ln -sf /lib/x86_64-linux-gnu/security/pam_userdb.so /lib/security/pam_userdb.so

    mkdir -p /var/run/vsftpd/empty

    cp -f /usr/local/akcs/csftp/system/vsftpd_login /etc/pam.d/vsftpd_login

    mkdir -p /home/<USER>/akuvox

    if ! id vsftpd; then
        useradd vsftpd -d /home/<USER>/bin/false
    fi

    chown -R vsftpd:vsftpd /home/<USER>
    chmod -R 700 /home/<USER>

    cp -f /usr/local/akcs/csftp/system/vsftpd.conf /etc/vsftpd.conf
    cp -f /usr/local/akcs/csftp/system/akuvox      /etc/vsftpd.d/akuvox
}


# 是否已安装标记
VSFTPD_INSTALLED_FLAG="/etc/vsftpd.d/.installed"
if [ -f "$VSFTPD_INSTALLED_FLAG" ]; then
    echo "vsftpd已经安装过,跳过安装步骤"
else
    install_vsftpd
    # 创建安装标记文件
    touch "$VSFTPD_INSTALLED_FLAG"
    echo "$(date)" > "$VSFTPD_INSTALLED_FLAG"
    echo "vsftpd首次安装完成,已创建安装标记"
fi




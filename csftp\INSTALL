INSTALL
=======

This file details how to build and install / run vsftpd from the vsftpd
distribution .tar.gz file.

Step 1) Build vsftpd.

Switch to the directory created when you unpacked the vsftpd .tar.gz file.
e.g.:

cd vsftpd-1.1.2

edit "builddefs.h" to handle compile-time settings (tcp_wrappers build,
etc).

Just type "make" (and mail me to fix it if it doesn't build ;-).
This should produce you a vsftpd binary. You can test for this, e.g.:

[chris@localhost vsftpd]$ ls -l vsftpd
-rwxrwxr-x    1 <USER>    <GROUP>       61748 Sep 27 00:26 vsftpd

Step 2) Satisfy vsftpd pre-requisites
2a) vsftpd needs the user "nobody" in the default configuration. Add this
user in case it does not already exist. e.g.:

[root@localhost root]# useradd nobody
useradd: user nobody exists

2b) vsftpd needs the (empty) directory /usr/share/empty in the default
configuration. Add this directory in case it does not already exist. e.g.:

[root@localhost root]# mkdir /usr/share/empty/
mkdir: cannot create directory `/usr/share/empty': File exists

2c) For anonymous FTP, you will need the user "ftp" to exist, and have a
valid home directory (which is NOT owned or writable by the user "ftp").
The following commands could be used to set up the user "ftp" if you do not
have one:

[root@localhost root]# mkdir /var/ftp/
[root@localhost root]# useradd -d /var/ftp ftp

(the next two are useful to run even if the user "ftp" already exists).
[root@localhost root]# chown root.root /var/ftp
[root@localhost root]# chmod og-w /var/ftp

Step 3) Install vsftpd config file, executable, man page, etc.

Running "make install" will try to copy the binary, man pages, etc. to
somewhere sensible.
Or you might want to copy these things by hand, e.g.:
cp vsftpd /usr/local/sbin/vsftpd
cp vsftpd.conf.5 /usr/local/man/man5
cp vsftpd.8 /usr/local/man/man8

"make install" doesn't copy the sample config file. It is recommended you
do this:
cp vsftpd.conf /etc

Step 4) Smoke test (without an inetd).

vsftpd can run standalone or via an inetd (such as inetd or xinetd). You will
typically get more control running vsftpd from an inetd. But first we will run
it without, so we can check things are going well so far.
Edit /etc/vsftpd.conf, and add this line at the bottom:

listen=YES

This tells vsftpd it will NOT be running from inetd.
Right, now let's try and run it!
Log in as root.
Make sure you are not running other FTP servers (or vsftpd will not be able
to use the FTP port, 21).
Run the binary from wherever you put it, e.g.:

[root@localhost root]# /usr/local/sbin/vsftpd &
[1] 2104

If all is well, you can now connect! e.g.:

[chris@localhost chris]$ ftp localhost
Connected to localhost (127.0.0.1).
220 (vsFTPd 1.1.1)
Name (localhost:chris): ftp
331 Please specify the password.
Password:
230 Login successful. Have fun.
Remote system type is UNIX.
Using binary mode to transfer files.
ftp> ls
227 Entering Passive Mode (127,0,0,1,229,133)
150 Here comes the directory listing.
d--x--x--x    2 <USER>        <GROUP>            4096 Jan 14  2002 bin
d--x--x--x    2 <USER>        <GROUP>            4096 Apr 21 20:52 etc
drwxr-xr-x    2 <USER>        <GROUP>            4096 Apr 21 20:52 lib
drwxr-sr-x    2 0        50           4096 Jul 26 22:58 pub
226 Directory send OK.
ftp>

Step 5) Run from an inetd of some kind (optional - standalone mode is now
recommended)

You may want to run the binary from an inetd of some kind, because this can
give you extra features - e.g. xinetd has a lot of settings. (Note that
vsftpd's inbuilt listener covers most of the more useful xinetd settings).

5a) If using standard "inetd", you will need to edit /etc/inetd.conf, and add
a line such as:

ftp stream tcp nowait root /usr/sbin/tcpd /usr/local/sbin/vsftpd

(Make sure to remove or comment out any existing ftp service lines. If you
don't have tcp_wrappers installed, or don't want to use them, take out the
/usr/sbin/tcpd part).

inetd will need to be told to reload its config file:
kill -SIGHUP `pidof inetd`

5b) If using "xinetd", you can follow a provided example, by looking at the
file EXAMPLE/INTERNET_SITE/README. Various other examples show how to leverage
the more powerful xinetd features.


Step 6) Set up PAM for local logins (optional)

If you are running vsftpd on a PAM enabled machine, you will need to have a
/etc/pam.d/ftp file present, otherwise non-anonymous logins will fail. [NOTE -
if you have an older version of PAM, that file might be /etc/pam.conf]

For a standard setup, you can just copy a provided example file:
cp RedHat/vsftpd.pam /etc/pam.d/ftp


Step 7) Customize your configuration

As well as the above three pre-requisites, you are recommended to install a
config file. The default location for the config file is /etc/vsftpd.conf.
There is a sample vsftpd.conf in the distribution tarball. You probably want
to copy that to /etc/vsftpd.conf as a basis for modification, i.e.:

cp vsftpd.conf /etc

The default configuration allows neither local user logins nor anonymous
uploads. You may wish to change these defaults.

Other notes
===========

Tested platforms (well, it builds)
- Any modern, well featured platform should work fine! Recent versions of
the platforms listed below, and often older ones, should work fine.
- Fedora Core
- RedHat Linux
- RedHat Enterprise Linux
- Solaris / GNU tools (Solaris 8 or newer)
- SuSE Linux
- Debian Linux
- OpenBSD
- FreeBSD
- NetBSD
- HP-UX / GNU tools
- IRIX / GNU tools
- AIX / GNU tools
- Mac OS X (note; older versions have setgroups() problem. 10.3.4 reported OK)


This example shows how to set up vsftpd / PAM with "virtual users".
A virtual user is a user login which does not exist as a real login on the
system. Virtual users can therefore be more secure than real users, beacuse
a compromised account can only use the FTP server.

Virtual users are often used to serve content that should be accessible to
untrusted users, but not generally accessible to the public.

Step 1) Create the virtual users database.
We are going to use pam_userdb to authenticate the virtual users. This needs
a username / password file in "db" format - a common database format.
To create a "db" format file, first create a plain text files with the
usernames and password on alternating lines.
See example file "logins.txt" - this specifies "tom" with password "foo" and
"fred" with password "bar".
Whilst logged in as root, create the actual database file like this:

db_load -T -t hash -f logins.txt /etc/vsftpd_login.db
(Requires the Berkeley db program installed).
NOTE: Many systems have multiple versions of "db" installed, so you may
need to use e.g. db3_load for correct operation. This is known to affect
some Debian systems. The core issue is that pam_userdb expects its login
database to be a specific db version (often db3, whereas db4 may be installed
on your system).

This will create /etc/vsftpd_login.db. Obviously, you may want to make sure
the permissions are restricted:

chmod 600 /etc/vsftpd_login.db

For more information on maintaing your login database, look around for
documentation on "Berkeley DB", e.g.
http://www.sleepycat.com/docs/utility/index.html


Step 2) Create a PAM file which uses your new database.

See the example file vsftpd.pam. It contains two lines:

auth required /lib/security/pam_userdb.so db=/etc/vsftpd_login
account required /lib/security/pam_userdb.so db=/etc/vsftpd_login

This tells PAM to authenticate users using our new database. Copy this PAM
file to the PAM directory - typically /etc/pam.d/

cp vsftpd.pam /etc/pam.d/ftp

(Note - if you set pam_service_name to e.g. vsftpd instead, you'll need to copy
to /etc/pam.d/vsftpd).


Step 3) Set up the location of the files for the virtual users.

useradd -d /home/<USER>
ls -ld /home/<USER>
(which should give):
drwx------    3 <USER>  <GROUP>      4096 Jul 30 00:39 /home/<USER>

We have created a user called "virtual" with a home directory "/home/<USER>".
Let's add some content to this download area:

cp /etc/hosts /home/<USER>
chown virtual.virtual /home/<USER>/hosts


Step 4) Create your vsftpd.conf config file.

See the example in this directory. Let's go through it line by line:

anonymous_enable=NO
local_enable=YES

This disables anonymous FTP for security, and enables non-anonymous FTP (which
is what virtual users use).

write_enable=NO
anon_upload_enable=NO
anon_mkdir_write_enable=NO
anon_other_write_enable=NO

These ensure that for security purposes, no write commands are allowed.

chroot_local_user=YES

This makes sure that the virtual user is restricted to the virtual FTP area
/home/<USER>

guest_enable=YES
guest_username=virtual

The guest_enable is very important - it activates virtual users! And
guest_username says that all virtual users are mapped to the real user
"virtual" that we set up above. This will also determine where on the
filesystem the virtual users end up - the home directory of the user
"virtual", /home/<USER>

listen=YES
listen_port=10021

This puts vsftpd in "standalone" mode - i.e. not running from an inetd. This
means you just run the vsftpd executable and it will start up. This also
makes vsftpd listen for FTP requests on the non-standard port of 10021 (FTP
is usually 21).

pasv_min_port=30000
pasv_max_port=30999

These put a port range on passive FTP incoming requests - very useful if
you are configuring a firewall.

Copy the example vsftpd.conf file to /etc:

cp vsftpd.conf /etc/


Step 5) Start up vsftpd.

Go to the directory with the vsftpd binary in it, and:

./vsftpd

If all is well, the command will sit there. If all is not well, you will
likely see some error message.


Step 6) Test.

Launch another shell session (or background vsftpd with CTRL-Z and then "bg").
Here is an example of an FTP session:

ftp localhost 10021
Connected to localhost (127.0.0.1).
220 ready, dude (vsFTPd 1.1.0: beat me, break me)
Name (localhost:chris): tom
331 Please specify the password.
Password:
230 Login successful. Have fun.
Remote system type is UNIX.
Using binary mode to transfer files.
ftp> pwd
257 "/"
ftp> ls
227 Entering Passive Mode (127,0,0,1,117,135)
150 Here comes the directory listing.
226 Transfer done (but failed to open directory).
ftp> size hosts
213 147
ftp>

Comments:
The password we gave was "foo".
Do not be alarmed by the "failed to open directory". That is because the
directory /home/<USER>
behaviour if we wanted using anon_world_readable_only=NO but maybe we want
it this way for security.
We can see that we have access to the "hosts" file we copied into the virtual
FTP area, via the size command.


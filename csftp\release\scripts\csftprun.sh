#!/bin/bash

#守护脚本启动前，先设置配置文件
/bin/bash /usr/local/akcs/csftp/scripts/sedconf.sh

#安装vsftpd
/bin/bash /usr/local/akcs/csftp/scripts/install_vsftpd.sh


PROCESS_NAME=vsftpd
PROCESS_START_CMD="/usr/local/akcs/csftp/scripts/csftpctl.sh start"
LOG_FILE=/var/log/csftp_run_daemon.log
PROCESS_COMMON_SCRIPTS="/usr/local/akcs/csftp/scripts/common.sh"
LOG_BACK_SCRIPTS="/usr/local/akcs/csftp/scripts/log_back.sh"
SERVERIP=`cat /etc/ip | grep SERVERIP=  | awk -F '=' '{print $2}'`
csftp_path="/var/log/csftplog"
CSFTP_BIN='/usr/local/akcs/csftp/bin/vsftpd'

#一定要是绝对路径，不然就变成要指定目录执行这个run
#source $PROCESS_COMMON_SCRIPTS
#source $LOG_BACK_SCRIPTS

mkdir -p /var/run/vsftpd/empty

$CSFTP_BIN >/dev/null 2>&1



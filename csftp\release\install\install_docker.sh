#!/bin/bash

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
DOCKER_IMG=$3
CONTAINER_NAME=csftp


# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

IP_FILE=/etc/ip
INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
APP_NAME=vsftpd    # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csftp
LOG_PATH=/var/log/csftplog
DATA_DIR=/usr/local/akcs/csstorage/data
FTP_DATA_DIR=/usr/local/akcs/csstorage/ftp
FTP_DATA_DIR_PIC=/usr/local/akcs/csstorage/ftp/data
FTP_DATA_DIR_OFFLINE=/usr/local/akcs/csstorage/ftp/data/offlinelog
CTRL_SCRIPT=csftpctl.sh
RUN_SCRIPT=csftprun.sh
RUN_SCRIPT_PATH=$APP_HOME/scripts/$RUN_SCRIPT
SIGNAL=${SIGNAL:-TERM}


# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install csftp."

echo '读取配置'

if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

CSFTP_SERVER_DOMAIN=$(grep_conf 'CSFTP_SERVER_DOMAIN' $INSTALL_CONF)
# 防止 domain 配置为空的情况出现
if [ -z "$CSFTP_SERVER_DOMAIN" ]; then
    echo "Please input your csftp Domain."
    exit 1;
fi

ENV_CONF_PARAM="
-e SERVERIP=$(grep_conf 'SERVERIP' $IP_FILE)
-e SERVERIPV6=$(grep_conf 'SERVERIPV6' $IP_FILE)
-e CSFTP_SERVER_DOMAIN=$CSFTP_SERVER_DOMAIN
-e MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
-e ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $INSTALL_CONF)
-e BEANSTALKD_IP=$(grep_conf 'BEANSTALKD_IP' $INSTALL_CONF)
-e REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
-e ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
-e SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
-e ENABLE_DBPROXY=$(grep_conf 'ENABLE_DBPROXY' $INSTALL_CONF)
-e DBPROXY_INNER_IP=$(grep_conf 'DBPROXY_INNER_IP' $INSTALL_CONF)
"

ENV_LOAD_PARAM="
-v /usr/share/zoneinfo:/usr/share/zoneinfo
-v /var/log/csftplog:/var/log/csftplog
-v /var/core:/var/core
-v /etc/ip:/etc/ip
-v /etc/kdc.conf:/etc/kdc.conf 
-v /bin/crypto:/bin/crypto
-v /usr/local/akcs/csstorage/ftp:/usr/local/akcs/csstorage/ftp
"

echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

if [ `docker ps -a | grep -w $CONTAINER_NAME | wc -l` -gt 0 ];then
    old_image_id=$(docker inspect --format='{{.Image}}' $CONTAINER_NAME)
    docker stop $CONTAINER_NAME;
    docker rm -f $CONTAINER_NAME;
    docker rmi -f $old_image_id || true
fi

# 停止旧的守护脚本和服务
echo "停止守护脚本 $RUN_SCRIPT"
run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
if [ -n "$run_pids" ]; then
    kill -9 $run_pids
    sleep 2
fi
echo "停止服务 $APP_NAME"
ps aux | grep /bin/vsftpd | awk '{print $2}' | xargs kill || true
sleep 3
sed -i '/csftprun.sh/d' /etc/init.d/rc.local


docker run -d -e TZ=Asia/Shanghai ${ENV_CONF_PARAM} ${ENV_LOAD_PARAM} --restart=always --net=host --name ${CONTAINER_NAME} ${DOCKER_IMG} /bin/bash /usr/local/akcs/csftp/scripts/csftprun.sh


echo "$APP_NAME install complete."


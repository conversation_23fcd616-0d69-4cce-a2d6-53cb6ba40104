# vsftpd is the secure FTP server.
service ftp
{
        disable                 = no
        socket_type             = stream
        wait                    = no
        user                    = root
        server                  = /usr/local/sbin/vsftpd
        per_source              = 5
        instances               = 200
        no_access               = ***********
        banner_fail             = /etc/vsftpd.busy_banner
        log_on_success          += PID HOST DURATION
        log_on_failure          += HOST
}

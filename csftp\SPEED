- See also BENCHMARKS

This FTPd should be very performant. The reasons for this are below, followed
by specific benchmarks as and when I get them.

1) Generally, it is a fairly minimal FTPd. There should not be much code and/or
syscall bloat.

2) For binary downloads, Linux sendfile() is used. This is a lot lighter on
CPU/syscall usage than your regular read()/write() loop.

3) The "ls" command is fully internal. That is to say, an external "ls" command
does not need to be launch. Launching an external process is costly because
of the fork(), exec(), ELF loader startup, etc.


It is not all good news, of course. Potential sources of poor performance
include

1) Overhead of two processes per session (in some common configurations).

2) Excessive heap usage hidden behind the string API.


BENCHMARKS
==========

1) vsftpd downloads ASCII data at at least twice the rate of wu-ftpd.

2) vsftpd has achieved 86Mbyte/sec download over Gigabit ethernet between
Linux-2.4.x boxes (thanks to sendfile())

3) vsftpd has smaller virtual memory usage (and RSS, it seems)

4) Various reports have trickled in and indicate that vsftpd thumps wu-ftpd
in performance tests.


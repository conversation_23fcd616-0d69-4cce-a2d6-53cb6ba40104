#include "CachePool.h"
#include "ConnectionPool.h"
#include "RldbQuery.h"
#include "akcs.h"
#include "util.h"
#include "ConfigFileReader.h"
#include "AkLogging.h"
#include "EtcdCliMng.h"
#include "AkcsMonitor.h"
#include "AkcsBussiness.h"
#include "beanstalk.hpp"
#include "evnsq/producer.h"
#include <sstream>
#include <stdio.h>
#include <memory.h>
#include <grp.h>
#include <pwd.h>
#include "AkcsDnsResolver.h"
#include "EtcdCliMng.h"
#include "akcs_attach_collect.h"
#include "HttpServer.h"


CAkEtcdCliManager* g_etcd_cli_mng = nullptr;
extern const char *g_conf_db_addr;
int g_etcd_dns_res = 0;

#define WEEK_SECOND 604800
static const char mysql_conf_file[] = "/usr/local/akcs/csftp/conf/csftp.conf";
static const char redis_conf_file[] = "/usr/local/akcs/csftp/conf/csftp_redis.conf";
static const std::string ACC_TCP_BUSSINESS = "csftp_tcp";
static const uint32_t ACC_TCP_PERIOD = 3600;//一个小时,60 * 60s
static const uint32_t ACC_TCP_NUM = 10;//一段时间内,判断为错误的次数达到10次，即认为是黑客攻击
static const uint32_t ACC_TCP_KEY_EXPIRE = 86400; //一天内让没有被判断为错误的业务对象释放出去,60 * 60 * 24s
char FTP_ATTACH_BUSSINESS[] = "csftp_ftp_attack";
static const uint32_t FTP_ATTACH_BUSSINESS_PERIOD = 3600;//一个小时,60 * 60s
//一段时间内,判断为错误的次数达到3次，即认为是黑客攻击。当前这个是错误的账号 必然是攻击
static const uint32_t FTP_ATTACH_BUSSINESS_NUM = 3;
static const uint32_t FTP_ATTACH_BUSSINESS_KEY_EXPIRE = 86400; //一天内让没有被判断为错误的业务对象释放出去,60 * 60 * 24s

static const std::string AKCS_ATTACK_IP_TUBE = "akcs_attack_ips"; //beanstalkd的tube,专用于akcs业务判断攻击者的来源ip用
static const uint32_t AKCS_ATTACK_IP_RELEASE = 172800; //2天后将攻击者的ip从iptables列表中删除掉,60 * 60 * 24 *2s，延迟时间长度
Beanstalk::Client* g_beanstalkd_client_ptr = nullptr;
AKCS_CONF stAKCSConf;

int OnRouteMQAlarmMessage(const evnsq::Message* msg)
{
    AK_LOG_INFO << "Received a message, id=" << msg->id << " message=[" << msg->body.ToString() << "]";
    return 0;
}

void MQProduceInit()
{
    evpp::EventLoop nsq_loop;
    evnsq::Option op;
    evnsq::Producer client(&nsq_loop, op);
    client.SetMessageCallback(&OnRouteMQAlarmMessage);
    std::string inner_addr = GetEth0IPAddr();
    std::string nsqd_tcp_addr = inner_addr + std::string(":8514");
    client.ConnectToNSQDs(nsqd_tcp_addr);
    AKCS::Singleton<SystemMonitor>::instance().Init(&client);
    nsq_loop.Run();
}

void TcpAttackCallback(const std::string& bussiness, const std::string& key)
{
    //char iptables_action[64] = {0};
    //::snprintf(iptables_action, 64, "iptables -I INPUT -s %s -j DROP", key.c_str());
    //::system(iptables_action);
    //加入延迟消息队列
    //modified by chenyc,2022.08.31,目前已经没有用了，全部走运维通道拉黑
    // AK_LOG_WARN << "there is one attack happens, iptables input drop, ip is " << key;
    //g_beanstalkd_client_ptr->put(key, 0, AKCS_ATTACK_IP_RELEASE, 10);
}

void FTPAttackCallback(const std::string& bussiness, const std::string& key)
{
    AK_LOG_WARN << "there is one attack happens, iptables input drop,bussiness is " << bussiness <<", ip is " << key;
    AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorIptables("vsftpd", key);
}

void glogInit(const char* argv)
{
    google::InitGoogleLogging(argv);
    google::SetLogDestination(google::GLOG_INFO, "/var/log/csftplog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/csftplog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/csftplog/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/csftplog/FATAL");
    FLAGS_logbufsecs = 0;
    google::SetStderrLogging(google::GLOG_WARNING); // 输出到标准输出的时候大于INFO级别的都输出;
    FLAGS_max_log_size = 10;    //单日志文件最大10M
}

void glogClean()
{
    google::ShutdownGoogleLogging();
}

int OnDnsChange(const std::vector <std::string>& addrs)
{
    std::vector <std::string> new_addrs;
    std::stringstream etcd_ips_str;
    for (auto &ip : addrs)
    {
        std::string etcd_addr = ip;
        etcd_addr += ":8507";
        new_addrs.push_back(etcd_addr);
        etcd_ips_str << etcd_addr << ",";
        AK_LOG_INFO << "etcd new ip: " << etcd_addr;
        g_etcd_dns_res = 1;//解析过了
    }    
    //更新为ip串 
    snprintf(stAKCSConf.szEtcdServerAddr, sizeof(stAKCSConf.szEtcdServerAddr), "%s", etcd_ips_str.str().c_str());
    AK_LOG_INFO << "etcd addrs: " << stAKCSConf.szEtcdServerAddr;
    
    if (g_etcd_cli_mng && new_addrs.size() > 0)
    {
        g_etcd_cli_mng->UpdateEtcdAddrs(new_addrs);
    }
    
}

void DnsResolver()
{
    CConfigFileReader config_file(mysql_conf_file);
    //etcd集群信息形如:etcd_srv_net=127.0.0.1:8525,************:8523,...
    Snprintf(stAKCSConf.szEtcdServerAddr, sizeof(stAKCSConf.szEtcdServerAddr),  config_file.GetConfigName("etcd_srv_net"));
    int need_res = 0;
    std::string etcd_net = stAKCSConf.szEtcdServerAddr;
    for(unsigned int i = 0; i < etcd_net.size(); i++)
    {
        if(etcd_net[i] >= 'a' && etcd_net[i] <= 'z')
        {
            need_res = 1;
            break;
        }
    }
    
    if (need_res)
    {
        g_etcd_dns_res = 0;
    }
    else
    {
        //不需要解析
        g_etcd_dns_res = 1;
    }
    
    if (g_etcd_dns_res == 0)
    {
        evpp::EventLoop dns_loop;
        AkcsDNSResolver dns(stAKCSConf.szEtcdServerAddr, &dns_loop);
        dns.SetOnChange(OnDnsChange);
        dns_loop.Run();
    }
}

int DaoReInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoReInit failed.";
        return -1;
    }
    conn_pool->ReInit(stAKCSConf.szDbIP, stAKCSConf.nDbPort);
    return 0;
}
void UpdateOuterConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    //进行比较,确定配置型变更后需要的动作
    if((::strcmp(conf_tmp.db_ip, stAKCSConf.szDbIP) != 0) || (conf_tmp.db_port != stAKCSConf.nDbPort))
    {
        Snprintf(stAKCSConf.szDbIP, sizeof(stAKCSConf.szDbIP),  conf_tmp.db_ip);
        stAKCSConf.nDbPort = conf_tmp.db_port;
        DaoReInit();
    }
}
int LoadConfFromConfSrv()
{
    SrvDbConf conf_tmp;
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    if((strlen(conf_tmp.db_ip) == 0) || (conf_tmp.db_port == 0))
    {
        return -1;
    }
    Snprintf(stAKCSConf.szDbIP, sizeof(stAKCSConf.szDbIP),  conf_tmp.db_ip);
    stAKCSConf.nDbPort = conf_tmp.db_port;
    return 0;
}
void ConfSrvInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(stAKCSConf.szEtcdServerAddr);
    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_addr, UpdateOuterConfFromConfSrv);
}
void ConfWatch()
{
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}

int AkcsInit()
{
    //glog初始化
    glogInit("vsftpd");
       
	memset(&stAKCSConf, 0, sizeof(AKCS_CONF));
    std::thread dnsThread = std::thread(DnsResolver);
    while(!g_etcd_dns_res)
    {
        usleep(10);
    }   
    dnsThread.detach();
    ConfSrvInit();
    
    //配置初始化
    CConfigFileReader config_file(mysql_conf_file);
    Snprintf(stAKCSConf.szDbUserName, sizeof(stAKCSConf.szDbUserName),  config_file.GetConfigName("db_username"));
    Snprintf(stAKCSConf.szDbPassword, sizeof(stAKCSConf.szDbPassword),  config_file.GetConfigName("db_passwd"));
    Snprintf(stAKCSConf.szDbDatabase, sizeof(stAKCSConf.szDbDatabase),  config_file.GetConfigName("db_database"));
    Snprintf(stAKCSConf.szOuterIP, sizeof(stAKCSConf.szOuterIP),  config_file.GetConfigName("outerip"));
    Snprintf(stAKCSConf.outer_ipv6, sizeof(stAKCSConf.outer_ipv6),  config_file.GetConfigName("outeripv6"));
    Snprintf(stAKCSConf.outer_domain, sizeof(stAKCSConf.outer_domain),  config_file.GetConfigName("outer_domain"));
    Snprintf(stAKCSConf.szBeansTalkdIP, sizeof(stAKCSConf.szBeansTalkdIP),  config_file.GetConfigName("beanstalk_ip"));
    
    //读取目录配置并创建目录
    /*
    const char* path_num = config_file.GetConfigName("updir_num");
    stAKCSConf.npathnum = ATOI(path_num);
    const char* root_path = config_file.GetConfigName("up_dir");
    for(int i = 0; i < stAKCSConf.npathnum; i++)
    {
        char tmp_updir[24] = {0};
        sprintf(tmp_updir, "%s%s%d", root_path, "/pic_", i+1);
        Snprintf(stAKCSConf.szupdir[i], sizeof(stAKCSConf.szupdir[i]),  tmp_updir);
        if (access(stAKCSConf.szupdir[i], 0) == -1)
        {
            int flag = mkdir(stAKCSConf.szupdir[i], 0777);
            int stat = chmod(stAKCSConf.szupdir[i], 00777);
            struct passwd *user;
            struct group * data;
            user = getpwnam("vsftpd");
            data = getgrnam("nobody");
            int pri =  chown(stAKCSConf.szupdir[i], user->pw_uid, data->gr_gid);
            if (flag != 0 || stat != 0 || pri != 0)
            {
                AK_LOG_INFO << "create ftp dir fail!";
            }
        }
    }
    */
    if(LoadConfFromConfSrv() != 0)
    {
        Snprintf(stAKCSConf.szDbIP, sizeof(stAKCSConf.szDbIP),  config_file.GetConfigName("db_ip"));
        const char* db_port = config_file.GetConfigName("db_port");
        stAKCSConf.nDbPort = ATOI(db_port);
    }
    const char* beanstalk_port = config_file.GetConfigName("beanstalk_port");
    stAKCSConf.nBeansTalkdPort = ATOI(beanstalk_port);

    //etcd初始化
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(stAKCSConf.szEtcdServerAddr);//"http://ip:port"
    const char* ftp_port= ":21";
    std::string outer_addr = stAKCSConf.szOuterIP;
    outer_addr += ftp_port;    //ftp监听端口
    char outer_reg_info[128] = {0};
    snprintf(outer_reg_info, sizeof(outer_reg_info), "%s%s", "/akcs/csftp/outerip/", outer_addr.c_str());
    char outer_addr_tmp[128];
    snprintf(outer_addr_tmp, sizeof(outer_addr_tmp), "&[%s]:21&%s:21", stAKCSConf.outer_ipv6, stAKCSConf.outer_domain);
    outer_addr += outer_addr_tmp;
    g_etcd_cli_mng->RegKeepAliveSrv(outer_reg_info, outer_addr, 10, csbase::REG_OUTER, NULL);

    //Redis初始化
    if (0 != CacheManager::getInstance()->Init(redis_conf_file, "csftpCacheInstances"))
    {
        AK_LOG_WARN << "init instance failed";
        glogClean();
        return -1;
    }

    //Mysql初始化
    ConnPool* gConnPool = GetDBConnPollInstance();
    if (NULL == gConnPool)
    {
        glogClean();
        return -1;
    }
    gConnPool->Init(stAKCSConf.szDbIP, stAKCSConf.szDbUserName, stAKCSConf.szDbPassword, stAKCSConf.szDbDatabase, stAKCSConf.nDbPort, 1, "csftp");

    g_beanstalkd_client_ptr = new Beanstalk::Client(stAKCSConf.szBeansTalkdIP, stAKCSConf.nBeansTalkdPort);
    g_beanstalkd_client_ptr->use(AKCS_ATTACK_IP_TUBE);
    AKCS::Singleton<BussinessLimit>::instance().InitBussiness(ACC_TCP_BUSSINESS, ACC_TCP_PERIOD, ACC_TCP_NUM, ACC_TCP_KEY_EXPIRE, TcpAttackCallback);
    //added by chenyc,2022.08.16,ftp安全防护逻辑,两个方面会引用该业务:1.ftp认证失败,2.图片上传不符合ak的安全规则
    AKCS::Singleton<BussinessLimit>::instance().InitBussiness(FTP_ATTACH_BUSSINESS, FTP_ATTACH_BUSSINESS_PERIOD,FTP_ATTACH_BUSSINESS_NUM, FTP_ATTACH_BUSSINESS_KEY_EXPIRE, FTPAttackCallback);

    std::thread mqProduceThread = std::thread(MQProduceInit);
    mqProduceThread.detach();
    std::thread conf_watch_thread = std::thread(ConfWatch);
    conf_watch_thread.detach();

    std::thread unix_socket_thread = std::thread(StartAttachSocketServer);
    unix_socket_thread.detach();

    //起http服务线程
    std::thread start_http_sever = std::thread(startHttpServer);
    start_http_sever.detach();
    return 0;
}

int SelectDevOuterIp(const char* outer_ip, int is_ipv6)
{
    int ret = 0;
    std::stringstream ip;
    std::stringstream streamSQL;
    std::string mac;

    if (is_ipv6)
    {
        ip << "[" << outer_ip << "]";   //ipv6需要加[]
    }
    else
    {
        ip << "[::ffff:" << outer_ip << "]"; //统一用ipv6形式
    }

    CacheManager* pCacheManager = CacheManager::getInstance();
    CacheConn* pCacheConn = pCacheManager->GetCacheConn("dev_outerip"); //获取与redis实例的tcp连接

    if (pCacheConn)
    {
        mac = pCacheConn->get(ip.str());
    }
    if (!mac.empty())
    {
        pCacheManager->RelCacheConn(pCacheConn);
        return 1;
    }

    //redis没有，再去Mysql找
    streamSQL << "SELECT ID FROM PersonalDevices "
              << "WHERE outerIP = '"
              << ip.str()
              << "' limit 1;";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* pTmpConn = conn.get();
    if (nullptr == pTmpConn)
    {
        pCacheManager->RelCacheConn(pCacheConn);
        return ret;
    }

    CRldbQuery query(pTmpConn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        ret = 1;
    }
    else
    {
        std::stringstream streamSQL2;
        streamSQL2 << "SELECT ID FROM Devices "
                   << "WHERE outerIP = '"
                   << ip.str()
                   << "' limit 1;";
        query.Query(streamSQL2.str());
        if (query.MoveToNextRow())
        {
            ret = 1;
        }
    }
    ReleaseDBConn(conn);

    if (0 == ret)
    {
        AK_LOG_INFO << "ftp connect defense, ip: " << ip.str();
        if (!is_ipv6)
        {
            std::string remote_addr = outer_ip;
            AKCS::Singleton<BussinessLimit>::instance().AddBussiness(ACC_TCP_BUSSINESS, remote_addr);
        }
        else
        {
            std::string remote_addr = ip.str();
            std::size_t found = remote_addr.find("[::ffff:");
            if (found != std::string::npos)
            {
                remote_addr = remote_addr.substr(sizeof("[::ffff:") - 1); // **************]
                remote_addr = remote_addr.substr(0, remote_addr.size() - 1);//**************
                AKCS::Singleton<BussinessLimit>::instance().AddBussiness(ACC_TCP_BUSSINESS, remote_addr);
            }
        }

    }
    else
    {
        pCacheConn->set(ip.str(), "MAC");
        pCacheConn->expire(ip.str(), WEEK_SECOND);
    }

    pCacheManager->RelCacheConn(pCacheConn);
    return ret;
}

void ClientOverLimit(int num,int max)
{
    char limit_str[64];
    snprintf(limit_str, sizeof(limit_str), "There are more than %d clients. max clients set %d", num, max);
    AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("vsftpd", limit_str, AKCS_MONITOR_ALARM_CONNECT_OVERFLOW_FTP);
}

void AddBussiness(const char* bussiness, uint32_t bussiness_len, const char* key, uint32_t ken_len)
{
    AK_LOG_WARN << "ftp client req is invalid, bussiness is " << bussiness << ", key is " << key;
    AKCS::Singleton<BussinessLimit>::instance().AddBussiness(bussiness, key);
}

void AddFtpAttchBussiness(const char* key, uint32_t key_len)
{
    AddBussiness(FTP_ATTACH_BUSSINESS, strlen(FTP_ATTACH_BUSSINESS), key, key_len);
}

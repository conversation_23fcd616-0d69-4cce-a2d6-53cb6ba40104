This example shows how to extend the "VIRTUAL_USERS" example to reflect
a slightly more complex setup.

Let's assume that we want two types of virtual user - one that can only browse
and download content, and another that can upload new content as well as
download existing content.

To achieve this setup, we can use use of vsftpd's powerful per-user
configurability (new in v1.1.0).

In the previous virtual user example, we created two users - tom and fred.
Let's say that we want fred to have write access to upload new files whilst
tom can only download.

Step 1) Activate per-user configurability.

To activate this powerful vsftpd feature, add the following to
/etc/vsftpd.conf:
user_config_dir=/etc/vsftpd_user_conf

And, create this directory:

mkdir /etc/vsftpd_user_conf


Step 2) Give tom the ability to read all files / directories.

At the end of the last example, we noted that the virtual users can only
see world-readable files and directories. We could make the /home/<USER>
directory world readable, and upload files with world-read permission. But
another way of doing this is giving tom the ability to download files which
are not world-readable.

For the tom user, supply a config setting override for
anon_world_readable_only:

echo "anon_world_readable_only=NO" > /etc/vsftpd_user_conf/tom

Check it out - login as tom and now "ls" will return a directory listing!
Log in as fred and it won't.
NOTE - restart vsftpd to pick up the config setting changes to
/etc/vsftpd.conf. (Advanced users can send SIGHUP to the vsftpd listener
process).


Step 3) Give fred the ability to read all files / directories and create
new ones but not interfere with existing files.

echo "anon_world_readable_only=NO" > /etc/vsftpd_user_conf/fred
echo "write_enable=YES" >> /etc/vsftpd_user_conf/fred
echo "anon_upload_enable=YES" >> /etc/vsftpd_user_conf/fred

Check it out - login as tom and you can't upload. Log in as fred and you can!
Try and delete a file as both tom and fred - you can't.


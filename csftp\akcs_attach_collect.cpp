#include "akcs_attach_collect.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <arpa/inet.h>
#include "AkLogging.h"
#include "akcs.h"

#define PORT 24
#define MSG_MAX_SIZE 1024

int StartAttachSocketServer() {
    int server_fd, new_socket;
    struct sockaddr_in address;
    int addrlen = sizeof(address);
    char buffer[MSG_MAX_SIZE] = {0};


    if ((server_fd = socket(AF_INET, SOCK_STREAM, 0)) == 0) {
        AK_LOG_WARN << "createt unix socket error.";
        return -1;
    }

    int opt = 1;  
    setsockopt(server_fd, SOL_SOCKET, SO_REUSEADDR, (const void *)&opt, sizeof(opt));

    address.sin_family = AF_INET;
    address.sin_addr.s_addr = inet_addr("127.0.0.1");
    address.sin_port = htons(PORT);

    while (1)
    {
        // 绑定 socket 到端口 
        if (bind(server_fd, (struct sockaddr *)&address, addrlen) < 0) {
            AK_LOG_WARN << "unix socket bind error " << errno;
            sleep(1);
            continue;//防止启动时候绑定不了 这样攻击的ip就收集不了了
        }
        else
        {
            break;
        }
    }

    // 监听连接
    if (listen(server_fd, 1000) < 0) {
        AK_LOG_WARN << "unix socket listen error";
        close(server_fd);
        return -1;
    }
    AK_LOG_INFO << "server start ";


    while (1)
    {
        int read_fd = accept(server_fd, (struct sockaddr*)&address, (socklen_t*)&addrlen);
        if (read_fd < 0)
        {
            AK_LOG_WARN << "accept failed";
            continue;
        }

        memset(buffer, 0, MSG_MAX_SIZE);
        int len = read(read_fd, buffer, sizeof(buffer));
        close(read_fd);

        AddFtpAttchBussiness(buffer, strlen(buffer));

    }
    close(server_fd);
    return 0;
}

int PutAttachSocketMessage(char *message) {
    if (message == nullptr)
    {
        return 0;
    }
    int sock = 0;
    struct sockaddr_in serv_addr;
    char buffer[MSG_MAX_SIZE] = {0};

    // 创建 socket
    if ((sock = socket(AF_INET, SOCK_STREAM, 0)) < 0) {
        AK_LOG_WARN << "client socket bind error " << errno;
        return -1;
    }

    serv_addr.sin_family = AF_INET;
    serv_addr.sin_port = htons(PORT);

	serv_addr.sin_addr.s_addr = inet_addr("127.0.0.1");


    // 连接到服务器
    if (connect(sock, (struct sockaddr *)&serv_addr, sizeof(serv_addr)) < 0) {
        AK_LOG_WARN << "client Connection Failed " << errno;  
        return -1;
    }

    send(sock, message, strlen(message), 0);
    close(sock);
    return 0;
}
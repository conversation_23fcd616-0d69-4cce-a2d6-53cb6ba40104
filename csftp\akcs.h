#ifndef __AKCS_H__
#define __AKCS_H__
#include <stdint.h> 

#define CSFTP_CONF_COMMON_LEN 64
#define CSFTP_FILEPATH_LENGTH 256

#ifdef __cplusplus
extern "C"
{
#endif

typedef struct AKCS_CONF_T
{
#define CSFTP_CONF_UPDIR_NUM 24
    /* DB配置项 */
    char szDbIP[CSFTP_CONF_COMMON_LEN];
    char szDbUserName[CSFTP_CONF_COMMON_LEN];
    char szDbPassword[CSFTP_CONF_COMMON_LEN];
    char szDbDatabase[CSFTP_CONF_COMMON_LEN];
    char szEtcdServerAddr[CSFTP_CONF_COMMON_LEN];
    char szOuterIP[CSFTP_CONF_COMMON_LEN];
    char outer_ipv6[CSFTP_CONF_COMMON_LEN];
    char outer_domain[CSFTP_CONF_COMMON_LEN];
    char szBeansTalkdIP[CSFTP_CONF_COMMON_LEN];
    int npathnum;
    char szupdir[CSFTP_CONF_UPDIR_NUM][CSFTP_CONF_COMMON_LEN];
    int  nBeansTalkdPort;
    int  nDbPort;
} AKCS_CONF;
//函数声明
int SelectDevOuterIp(const char* outer_ip, int is_ipv6); //查询设备外网ip是否存在
int AkcsInit();
void ClientOverLimit(int num,int max);
//added by chenyc,2022.08.16,vsftpd服务安全防护相关逻辑
void AddBussiness(const char* bussiness, uint32_t bussiness_len, const char* key, uint32_t ken_len);
void AddFtpAttchBussiness(const char* key, uint32_t key_len);

#ifdef __cplusplus
}
#endif

#ifdef AKCS_CHANGE_DIR
/*
name:xzr
date:2021/11/12
note:根据配置文件随机分配用户登录的根目录
*/
#define AKCS_CHANGE_DIR
#endif

#ifdef AKCS_RANDOM_NUMBER
/*
name:xzr
date:2021/11/12
note:添加获取随机数的接口
*/
#define AKCS_RANDOM_NUMBER
#endif

#ifndef AKCS_FTP_SECURITY_IMPROVE
/*
name:chenyc
date:2022/08/30
note:ftp服务安全提升操作
*/
#define AKCS_FTP_SECURITY_IMPROVE
#endif

#ifndef AKCS_FTP_MOVE_TO_CSSTORAGE
/*
name:zjq
date:2023/06/27
note:将ftp上传的文件移动到storage_data路径处理
*/
#define AKCS_FTP_MOVE_TO_CSSTORAGE
#endif


#ifndef AKCS_FTP_CHILD_CREATE_FD
/*
name:chenzhx
date:2024/12/10
note:允许子进程创建fd, 和配置文件里面的isolate_network一起使用(不进行网络隔离，让子进程和父进程进行网络通信 传输攻击的ip)
*/
#define AKCS_FTP_CHILD_CREATE_FD
#endif


#endif



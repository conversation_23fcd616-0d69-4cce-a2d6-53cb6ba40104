This example should quickly show you the possibilites of per-IP configuration
with vsftpd's tcp_wrappers integration. This is new with v1.1.3.

To use this, you need vsftpd built with tcp_wrappers! This is accomplished
by editing "builddefs.h" and changing

#undef VSF_BUILD_TCPWRAPPERS
to
#define VSF_BUILD_TCPWRAPPERS

And then rebuild. If you are lucky your vendor will have shipped the vsftpd
binary with this already done for you.

Next, to enable tcp_wrappers integration, you need this in your vsftpd.conf:

tcp_wrappers=YES

And you'll need a tcp_wrappers config file. An example one is supplied in this
directory: hosts.allow. It lives at /etc/hosts.allow.

Let's have a look at the example:

vsftpd: ***********: setenv VSFTPD_LOAD_CONF /etc/vsftpd_tcp_wrap.conf
vsftpd: ***********: DENY

The first line:
If a client connects from ***********, then vsftpd will apply the vsftpd
config file /etc/vsftpd_tcp_wrap.conf to the session! These settings are
applied ON TOP of the default vsftpd.conf.
This is obviously very powerful. You might use this to apply different
access restrictions for some IPs (e.g. the ability to upload).
Or you could give certain classes of IPs the ability to skip connection
limits (max_clients=0).
Or you could increase / decrease the bandwidth limiter for certain classes
of IPs.
You get the point :-)

The second line:
Denies the ability of *********** to connect. Very useful to take care of
troublemakers. And now you don't need xinetd to do it - hurrah.



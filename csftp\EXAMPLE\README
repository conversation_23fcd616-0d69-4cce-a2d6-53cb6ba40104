These subdirectories contain examples of vsftpd usage.
These examples are known to work on a RedHat 7.2 installation. Some of them
rely on xinetd and / or a highly functional version of PAM.

The examples should serve to illustrate how vsftpd becomes extremely powerful
when intregration with xinetd for connection handling and PAM for
authentication.

Contents
========
INTERNET_SITE         How you might configure vsftpd for an internet site.
INTERNET_SITE_NOINETD How to use vsftpd without xinetd.
PER_IP_CONFIG         How to apply different settings based on the connecting
                      IP address.
VIRTUAL_HOSTS         How to set up vsftpd with virtual hosting.
VIRTUAL_USERS         How to set up virtual users with vsftpd.
VIRTUAL_USERS_2       Advanced virtual users - different access rights.


#!/bin/bash
# 替换配置文件
echo '替换配置文件的配置'

# csftp.conf
sed -i "
    s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g
    s/^.*outerip=.*/outerip=${SERVERIP}/g
    s/^.*outeripv6=.*/outeripv6=${SERVERIPV6}/g
    s/^.*outer_domain=.*/outer_domain=${CSFTP_SERVER_DOMAIN}/g
    s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g
    s/^.*beanstalk_ip=.*/beanstalk_ip=${BEANSTALKD_IP}/g" /usr/local/akcs/csftp/conf/csftp.conf

# pasv_address 专有网络下 填写外网地址
sed -i "s/^.*pasv_address=.*/pasv_address=${SERVERIP}/g" /usr/local/akcs/csftp/system/vsftpd.conf

# dbproxy 配置
if [ "$ENABLE_DBPROXY" = "1" ]; then
    sed -i "
        s/^.*db_port=.*/db_port=3308/g
        s/^.*db_ip=.*/db_ip=${DBPROXY_INNER_IP}/g" /usr/local/akcs/csftp/conf/csftp.conf
else
    sed -i "
        s/^.*db_port=.*/db_port=3306/g
        s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g" /usr/local/akcs/csftp/conf/csftp.conf
fi

# redis 配置
sed -i "s/^.*dev_outerip_host=.*/dev_outerip_host=${REDIS_INNER_IP}/g" /usr/local/akcs/csftp/conf/csftp_redis.conf

# redis sentinel 配置
if [ "$ENABLE_REDIS_SENTINEL" = "1" ]; then
    sed -i "s/^.*sentinels=.*/sentinels=${SENTINEL_HOSTS}/g" /usr/local/akcs/csftp/conf/csftp_redis.conf
else
    sed -i "s/^.*sentinels=.*/sentinels=/g" /usr/local/akcs/csftp/conf/csftp_redis.conf
fi

This file contains information on the audit status of the code in this program.
Each file has an audit code of between 1 and 5, ranging from 1 being unaudited
and 5 being heavily audited by multiple competent people.

The important rule is that when a file is changed, the audit status goes back
to 1, _unless_ the change(s) are audited very carefully as they go in.

access.c                     2
ascii.c                      3
banner.c                     2
dirchange.c                  3
filestr.c                    3
ftpcmdio.c                   3
ftpdataio.c                  2
hash.c                       1
ipaddrparse.c                1
logging.c                    3
ls.c                         2
main.c                       3
netstr.c                     3
oneprocess.c                 3
parseconf.c                  2
postlogin.c                  2
postprivparent.c             3
prelogin.c                   3
privops.c                    2
privparent.c                 3
privsock.c                   3
secbuf.c                     3
secutil.c                    3
ssl.c                        1
standalone.c                 1
str.c                        2
strlist.c                    2
sysdeputil.c                 2
sysstr.c                     3
sysutil.c                    2
tunables.c                   3
twoprocess.c                 2
utility.c                    3


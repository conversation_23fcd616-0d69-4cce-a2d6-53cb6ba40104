#!/bin/bash

# ****************************************************************************
# Author        :   ji<PERSON><PERSON>.li
# Last modified :   2022-04-12
# Filename      :   install.sh
# Version       :
# Description   :   csftp 的安装脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


check_md5()
{
    newfile=$1
    oldfile=$2
    newmd5=$(md5sum "$newfile" | awk '{print $1}')
    oldmd5=$(md5sum "$oldfile" | awk '{print $1}')
    if [ "$oldmd5" != "$newmd5" ]; then
        return 1
    else
        return 0
    fi
}


install_vsftpd()
{
    if ! dpkg -l | grep db4.8; then
        dpkg -i "$PKG_ROOT"/system/libdb4.8_4.8.30-trusty1_amd64.deb
        dpkg -i "$PKG_ROOT"/system/libdb4.8++_4.8.30-trusty1_amd64.deb
        dpkg -i "$PKG_ROOT"/system/db4.8-util_4.8.30-11ubuntu1_amd64.deb
    fi

    if [ -d /etc/vsftpd.d ]; then
        rm -f /etc/vsftpd.d/vsftpd_login.db
    else
        mkdir /etc/vsftpd.d
    fi

    db4.8_load -T -t hash -f "$PKG_ROOT"/system/ftpvusers.txt /etc/vsftpd.d/vsftpd_login.db
    chmod 600 /etc/vsftpd.d/vsftpd_login.db

    mkdir -p /lib/security/
    ln -sf /lib/x86_64-linux-gnu/security/pam_userdb.so /lib/security/pam_userdb.so

    mkdir -p /var/run/vsftpd/empty

    cp -f "$PKG_ROOT"/system/vsftpd_login /etc/pam.d/vsftpd_login

    mkdir -p /home/<USER>/akuvox

    if ! id vsftpd; then
        useradd vsftpd -d /home/<USER>/bin/false
    fi

    chown -R vsftpd:vsftpd /home/<USER>
    chmod -R 700 /home/<USER>

    cp -f "$PKG_ROOT"/system/vsftpd.conf /etc/vsftpd.conf
    cp -f "$PKG_ROOT"/system/akuvox /etc/vsftpd.d/akuvox
}


# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

IP_FILE=/etc/ip
INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
APP_NAME=vsftpd    # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csftp
LOG_PATH=/var/log/csftplog
DATA_DIR=/usr/local/akcs/csstorage/data
FTP_DATA_DIR=/usr/local/akcs/csstorage/ftp
FTP_DATA_DIR_PIC=/usr/local/akcs/csstorage/ftp/data
FTP_DATA_DIR_OFFLINE=/usr/local/akcs/csstorage/ftp/data/offlinelog
CTRL_SCRIPT=csftpctl.sh
RUN_SCRIPT=csftprun.sh
RUN_SCRIPT_PATH=$APP_HOME/scripts/$RUN_SCRIPT
SIGNAL=${SIGNAL:-TERM}


# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install csftp."

echo '读取配置'

if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

SERVERIP=$(grep_conf 'SERVERIP' $IP_FILE)
SERVERIPV6=$(grep_conf 'SERVERIPV6' $IP_FILE)

CSFTP_SERVER_DOMAIN=$(grep_conf 'CSFTP_SERVER_DOMAIN' $INSTALL_CONF)
MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $INSTALL_CONF)
BEANSTALKD_IP=$(grep_conf 'BEANSTALKD_IP' $INSTALL_CONF)
REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
ENABLE_DBPROXY=$(grep_conf 'ENABLE_DBPROXY' $INSTALL_CONF)
DBPROXY_INNER_IP=$(grep_conf 'DBPROXY_INNER_IP' $INSTALL_CONF)

# 防止 domain 配置为空的情况出现
if [ -z "$CSFTP_SERVER_DOMAIN" ]; then
    echo "Please input your csftp Domain."
    exit 1;
fi


# 停止守护脚本和服务
echo "停止守护脚本 $RUN_SCRIPT"
run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
if [ -n "$run_pids" ]; then
    kill -9 $run_pids
    sleep 2
fi

echo "停止服务 $APP_NAME"
app_pids=$(pidof vsftpd || true)
if [ -n "$app_pids" ]; then
    kill -s $SIGNAL $app_pids
    sleep 2
fi


# 替换配置文件
echo '替换配置文件的配置'

sed -i "
    s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g
    s/^.*outerip=.*/outerip=${SERVERIP}/g
    s/^.*outeripv6=.*/outeripv6=${SERVERIPV6}/g
    s/^.*outer_domain=.*/outer_domain=${CSFTP_SERVER_DOMAIN}/g
    s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g
    s/^.*beanstalk_ip=.*/beanstalk_ip=${BEANSTALKD_IP}/g" "$PKG_ROOT"/conf/csftp.conf

# pasv_address 专有网络下 填写外网地址
sed -i "s/^.*pasv_address=.*/pasv_address=${SERVERIP}/g" "$PKG_ROOT"/system/vsftpd.conf

# dbproxy 配置
if [ "$ENABLE_DBPROXY" = "1" ]; then
    sed -i "
        s/^.*db_port=.*/db_port=3308/g
        s/^.*db_ip=.*/db_ip=${DBPROXY_INNER_IP}/g" "$PKG_ROOT"/conf/csftp.conf
else
    sed -i "
        s/^.*db_port=.*/db_port=3306/g
        s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g" "$PKG_ROOT"/conf/csftp.conf
fi

# redis 配置
sed -i "s/^.*dev_outerip_host=.*/dev_outerip_host=${REDIS_INNER_IP}/g" "$PKG_ROOT"/conf/csftp_redis.conf

# redis sentinel 配置
if [ "$ENABLE_REDIS_SENTINEL" = "1" ]; then
    sed -i "s/^.*sentinels=.*/sentinels=${SENTINEL_HOSTS}/g" "$PKG_ROOT"/conf/csftp_redis.conf
else
    sed -i "s/^.*sentinels=.*/sentinels=/g" "$PKG_ROOT"/conf/csftp_redis.conf
fi


# 安装 vsftpd
echo '安装 vsftpd'
if [ -f /usr/sbin/vsftpd ]; then
    service vsftpd stop
    rm -rf /usr/sbin/vsftpd
fi

if [ ! -d /usr/local/akcs ]; then
    mkdir /usr/local/akcs
fi

if [ ! -d /var/run/vsftpd/empty ]; then
    mkdir -p /var/run/vsftpd/empty
fi

install_vsftpd


echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

echo "删除旧的安装文件 $APP_HOME"
if [ -d $APP_HOME ]; then
    rm -rf $APP_HOME
fi

if [ -d /usr/local/akcs/csftp_scripts ]; then
    rm -rf /usr/local/akcs/csftp_scripts
fi

echo '复制安装包的文件'
if [ ! -d $APP_HOME ]; then
    mkdir -p $APP_HOME
fi

cp -rf "$PKG_ROOT"/conf $APP_HOME
cp -rf "$PKG_ROOT"/bin $APP_HOME
cp -rf "$PKG_ROOT"/lib $APP_HOME
cp -rf "$PKG_ROOT"/scripts $APP_HOME
cp -f "$PKG_ROOT"/version $APP_HOME

cd $APP_HOME/lib
if [ -f $APP_HOME/lib/libevpp.so ]; then
    ln -sf libevpp.so libevpp.so.0.7
fi
cd "$PKG_ROOT"

if [ ! -d $DATA_DIR ]; then
    mkdir -p $DATA_DIR
fi

if [ ! -d $FTP_DATA_DIR ]; then
    mkdir -p $FTP_DATA_DIR
fi

if [ ! -d $FTP_DATA_DIR_PIC ]; then
    mkdir -p $FTP_DATA_DIR_PIC
fi

if [ ! -d $FTP_DATA_DIR_OFFLINE ]; then
    mkdir -p $FTP_DATA_DIR_OFFLINE
fi

# md5 校验，避免拷贝不完全
if ! check_md5 "$PKG_ROOT"/bin/$APP_NAME $APP_HOME/bin/$APP_NAME; then
    echo "copy error!"
    echo "$PKG_ROOT/bin/$APP_NAME    copy failed."
    exit 1
fi

chmod 755 $APP_HOME
chmod -R 755 $APP_HOME/bin
chmod -R 755 $APP_HOME/scripts
chmod -R 777 $DATA_DIR
chmod -R 777 $FTP_DATA_DIR

echo '添加到开机启动'
if ! grep -q "$RUN_SCRIPT_PATH" /etc/init.d/rc.local; then
    echo "bash $RUN_SCRIPT_PATH &" >> /etc/init.d/rc.local
fi


# ----------------------------------------------------------------------------
# 启动服务
# ----------------------------------------------------------------------------
# core文件生效
if [ ! -d /var/core ]; then
    mkdir -p /var/core
    chmod -R 777 /var/core
fi

if ! grep -q 'kernel.core_pattern' /etc/sysctl.conf; then
    echo 'kernel.core_pattern = /var/core/core_%e_%p_%t' >> /etc/sysctl.conf
    sysctl -p
fi

if ! grep -q 'ulimit -c unlimited' /etc/profile; then
    echo 'ulimit -c unlimited' >> /etc/profile
fi

ulimit -c unlimited


echo '启动服务'
$APP_HOME/scripts/$CTRL_SCRIPT start
sleep 2

echo '检查服务的运行状态'
$APP_HOME/scripts/$CTRL_SCRIPT status

echo '启动守护脚本'
if ! ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    nohup bash $RUN_SCRIPT_PATH >/dev/null 2>&1 &
    sleep 2
fi

echo '检查守护脚本的运行状态'
if ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    echo '守护脚本运行中'
else
    echo '守护脚本运行失败'
    exit 1
fi

echo "$APP_NAME install complete."

